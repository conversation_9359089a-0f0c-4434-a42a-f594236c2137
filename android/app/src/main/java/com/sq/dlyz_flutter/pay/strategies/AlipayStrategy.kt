package com.sq.dlyz_flutter.pay.strategies

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import com.alipay.sdk.app.PayTask
import com.sq.dlyz_flutter.pay.*
import java.util.concurrent.Executors

/**
 * 支付宝支付策略实现
 */
class AlipayStrategy : PayStrategy {
    
    companion object {
        private const val TAG = "AlipayStrategy"
        private const val ALIPAY_PACKAGE_NAME = "com.eg.android.AlipayGphone"
    }
    
    private var appId: String = ""
    private var privateKey: String = ""
    private var isDebug: Boolean = false
    private val executor = Executors.newSingleThreadExecutor()
    private val mainHandler = Handler(Looper.getMainLooper())
    
    override fun getPaymentName(): String = "支付宝"
    
    override fun getPaymentType(): PaymentType = PaymentType.ALIPAY
    
    override fun initialize(context: Context, config: PayConfig) {
        this.appId = config.appId
        this.privateKey = config.privateKey
        this.isDebug = config.isDebug
        
        Log.d(TAG, "支付宝SDK初始化完成, AppId: $appId, Debug: $isDebug")
    }
    
    override fun pay(activity: Activity, payRequest: PayRequest, callback: PayCallback) {
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(privateKey)) {
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.INVALID_PARAMS,
                    orderId = payRequest.getOrderId(),
                    message = "支付宝配置参数不完整"
                )
            )
            return
        }
        
        // 验证支付数据格式
        val paymentJson = payRequest.getPaymentDataAsJson()
        if (paymentJson == null) {
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.INVALID_PARAMS,
                    orderId = payRequest.getOrderId(),
                    message = "支付数据格式无效"
                )
            )
            return
        }

        // 构建支付订单信息
        val orderInfo = if (isAlipayOrderString(payRequest.paymentData)) {
            // 如果服务端返回的是完整的支付宝订单信息字符串，直接使用
            payRequest.paymentData
        } else {
            // 如果是JSON格式，构建支付宝订单信息
            buildOrderInfoFromJson(paymentJson)
        }
        
        // 在子线程中执行支付
        executor.execute {
            try {
                 val payResult = PayTask(activity).payV2(orderInfo, true)
                
                // 回到主线程执行回调
                mainHandler.post {
                    handlePayResult(payResult, payRequest, callback)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "支付宝支付异常", e)
                mainHandler.post {
                    callback.onPayFailed(
                        PayResult(
                            status = PayStatus.FAILED,
                            orderId = payRequest.getOrderId(),
                            message = "支付异常: ${e.message}"
                        )
                    )
                }
            }
        }
    }

    override fun isSupported(context: Context): Boolean {
        return try {
            val packageManager = context.packageManager
            packageManager.getPackageInfo(ALIPAY_PACKAGE_NAME, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.d(TAG, "支付宝应用未安装")
            false
        }
    }
    
    override fun release() {
        executor.shutdown()
        Log.d(TAG, "支付宝策略资源已释放")
    }

    /**
     * 判断是否为支付宝订单字符串格式
     */
    private fun isAlipayOrderString(data: String): Boolean {
        return data.contains("app_id=") && data.contains("method=") && data.contains("biz_content=")
    }

    /**
     * 从JSON构建支付订单信息
     */
    private fun buildOrderInfoFromJson(paymentJson: org.json.JSONObject): String {
        // 如果JSON中包含完整的orderInfo字段，直接使用
        if (paymentJson.has("orderInfo")) {
            return paymentJson.optString("orderInfo")
        }

        // 否则从JSON字段构建订单信息
        val orderInfo = StringBuilder()
        orderInfo.append("app_id=\"$appId\"")
        orderInfo.append("&method=\"alipay.trade.app.pay\"")
        orderInfo.append("&charset=\"utf-8\"")
        orderInfo.append("&sign_type=\"RSA2\"")
        orderInfo.append("&timestamp=\"${System.currentTimeMillis()}\"")
        orderInfo.append("&version=\"1.0\"")

        // 通知地址
        val notifyUrl = paymentJson.optString("notify_url", paymentJson.optString("notifyUrl", ""))
        if (notifyUrl.isNotEmpty()) {
            orderInfo.append("&notify_url=\"$notifyUrl\"")
        }

        // 业务参数
        val bizContent = StringBuilder()
        bizContent.append("{")
        bizContent.append("\"out_trade_no\":\"${paymentJson.optString("out_trade_no", paymentJson.optString("orderId", ""))}\",")
        bizContent.append("\"total_amount\":\"${paymentJson.optString("total_amount", paymentJson.optString("amount", ""))}\",")
        bizContent.append("\"subject\":\"${paymentJson.optString("subject", paymentJson.optString("title", ""))}\",")
        bizContent.append("\"body\":\"${paymentJson.optString("body", paymentJson.optString("description", ""))}\",")
        bizContent.append("\"timeout_express\":\"${paymentJson.optString("timeout_express", "30m")}\"")
        bizContent.append("}")

        orderInfo.append("&biz_content=\"${bizContent}\"")

        // 如果JSON中包含签名，直接使用
        val sign = paymentJson.optString("sign", "")
        if (sign.isNotEmpty()) {
            orderInfo.append("&sign=\"$sign\"")
        }

        return orderInfo.toString()
    }
    
    /**
     * 处理支付结果
     */
    private fun handlePayResult(
        resultMap: Map<String, String>,
        payRequest: PayRequest,
        callback: PayCallback
    ) {
        val resultStatus = resultMap["resultStatus"] ?: ""
        val memo = resultMap["memo"] ?: ""
        
        when (resultStatus) {
            "9000" -> {
                // 支付成功
                callback.onPaySuccess(
                    PayResult(
                        status = PayStatus.SUCCESS,
                        orderId = payRequest.getOrderId(),
                        transactionId = resultMap["trade_no"] ?: "",
                        message = "支付成功",
                        rawData = resultMap.toString()
                    )
                )
            }
            "8000" -> {
                // 正在处理中
                callback.onPayPending(
                    PayResult(
                        status = PayStatus.PENDING,
                        orderId = payRequest.getOrderId(),
                        message = "支付结果确认中"
                    )
                )
            }
            "6001" -> {
                // 用户取消
                callback.onPayCancelled(
                    PayResult(
                        status = PayStatus.CANCELLED,
                        orderId = payRequest.getOrderId(),
                        message = "用户取消支付"
                    )
                )
            }
            else -> {
                // 支付失败
                callback.onPayFailed(
                    PayResult(
                        status = PayStatus.FAILED,
                        orderId = payRequest.getOrderId(),
                        message = memo.ifEmpty { "支付失败" }
                    )
                )
            }
        }
    }
}
